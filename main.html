<!DOCTYPE html>
<html>

<head>
    <title>政府评分日晕图</title>
    <script src="d3.v7.min.js"></script>
    <script src="jquery-3.7.1.min.js"></script>
    <script src="d3-v6-tip.js"></script>
    <link rel="stylesheet" href="d3-tip.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }

        .controls {
            text-align: center;
            margin-bottom: 20px;
        }

        #country-select {
            padding: 8px 12px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 5px;
            background-color: white;
            min-width: 200px;
        }

        #country-select:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .svg-container {
            text-align: center;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin: 0 auto;
            max-width: 1200px;
        }

        .datapath {
            cursor: pointer;
            transition: fill-opacity 0.3s ease;
        }

        .datapath:hover {
            fill-opacity: 1 !important;
            stroke-width: 2;
        }

        .datatext {
            font-family: 'Arial', sans-serif;
        }

        .scoretext {
            font-family: 'Arial', sans-serif;
        }
    </style>
</head>

<body>
    <h1>各国政府评分日晕图</h1>

    <div class="controls">
        <label for="country-select">选择国家：</label>
        <select id="country-select">
            <!-- 国家选项将通过JavaScript动态添加 -->
        </select>
    </div>

    <div class="svg-container">
        <svg width="1200" height="800" id="mainsvg"></svg>
        <div id="legend" style="margin-top: 20px; text-align: left; max-width: 800px; margin-left: auto; margin-right: auto;">
            <h3 style="text-align: center; margin-bottom: 15px;">图例说明</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; font-size: 14px;">
                <!-- 图例将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
    <script>
        const svg = d3.select('svg');
        const width = svg.attr('width');
        const height = svg.attr('height');
        const margin = { top: 60, right: 30, bottom: 60, left: 150 };
        const innerWidth = width - margin.left - margin.right;
        const innerHeight = height - margin.top - margin.bottom;
        const mainGroup = svg.append('g').attr('transform', `translate(${width / 2}, ${height / 2})`);
        const radius = Math.min(innerWidth, innerHeight) / 2 - 10;










        // 日晕图相关变量
        let root;

        // 创建弧形生成器
        const arc = d3.arc()
            .startAngle(d => d.x0)
            .endAngle(d => d.x1)
            .padAngle(d => Math.min((d.x1 - d.x0) / 2, 0.005))
            .innerRadius(d => d.y0)
            .outerRadius(d => d.y1);

        // 颜色方案
        const color = d3.scaleOrdinal(d3.schemeCategory10);

        // 填充颜色函数
        const fill = d => {
            while (d.depth > 1) d = d.parent;
            return color(d.data.text || d.data.label);
        };

        // 渲染日晕图函数
        const render = function(data) {
            // 清除之前的图形
            mainGroup.selectAll('*').remove();

            // 绘制弧形路径
            mainGroup.selectAll('.datapath')
                .data(root.descendants().filter(d => d.depth !== 0))
                .join('path')
                .attr('class', 'datapath')
                .attr('fill', fill)
                .attr('fill-opacity', 0.8)  // 设置透明度
                .attr('stroke', '#fff')
                .attr('stroke-width', 1)
                .attr('d', arc);

            // 添加文字标签
            mainGroup.selectAll('.datatext')
                .data(root.descendants()
                    .filter(d => d.depth && (d.x1 - d.x0) > Math.PI / 65 && (d.data.text || d.data.label).length < 25))
                .join('text')
                .attr('class', 'datatext')
                .attr('pointer-events', 'none')
                .attr('text-anchor', 'middle')
                .attr('font-size', d => {
                    const textLength = (d.data.text || d.data.label).length;
                    if (d.depth === 1) return '14px';
                    return textLength < 15 ? '11px' : '9px';
                })
                .attr('font-weight', d => d.depth === 1 ? 'bold' : 'normal')
                .attr('fill', '#333')
                .attr('transform', function(d) {
                    const x = (d.x0 + d.x1) / 2 * 180 / Math.PI;
                    const y = (d.y0 + d.y1) / 2;
                    return `rotate(${x - 90}) translate(${y},0) rotate(${x < 180 ? 0 : 180}) translate(0, 5)`;
                })
                .text(d => {
                    const text = d.data.text || d.data.label;
                    // 对于较长的文本进行截断
                    return text.length > 20 ? text.substring(0, 18) + '...' : text;
                });

            // 添加分数标签（仅对主要因子显示）
            mainGroup.selectAll('.scoretext')
                .data(root.descendants().filter(d => d.depth === 1))
                .join('text')
                .attr('class', 'scoretext')
                .attr('pointer-events', 'none')
                .attr('text-anchor', 'middle')
                .attr('font-size', '12px')
                .attr('font-weight', 'bold')
                .attr('fill', '#000')
                .attr('transform', function(d) {
                    const x = (d.x0 + d.x1) / 2 * 180 / Math.PI;
                    const y = d.y1 + 15; // 在外圈显示分数
                    return `rotate(${x - 90}) translate(${y},0) rotate(${x < 180 ? 0 : 180})`;
                })
                .text(d => d.data.score.toFixed(2));

            // 添加中心标题和总分
            const centerGroup = mainGroup.append('g').attr('class', 'center-info');

            centerGroup.append('text')
                .attr('text-anchor', 'middle')
                .attr('font-size', '18px')
                .attr('font-weight', 'bold')
                .attr('fill', '#333')
                .attr('y', -10)
                .text('总体评分');

            centerGroup.append('text')
                .attr('text-anchor', 'middle')
                .attr('font-size', '24px')
                .attr('font-weight', 'bold')
                .attr('fill', '#2E86AB')
                .attr('y', 15)
                .text(data.score.toFixed(3));

            // 生成图例
            generateLegend(data);
        };

        // 生成图例函数
        const generateLegend = function(data) {
            const legendContainer = document.querySelector('#legend div');
            legendContainer.innerHTML = '';

            data.children.forEach((factor, index) => {
                const legendItem = document.createElement('div');
                legendItem.style.display = 'flex';
                legendItem.style.alignItems = 'center';
                legendItem.style.marginBottom = '5px';

                const colorBox = document.createElement('div');
                colorBox.style.width = '20px';
                colorBox.style.height = '20px';
                colorBox.style.backgroundColor = color(factor.text || factor.label);
                colorBox.style.marginRight = '10px';
                colorBox.style.border = '1px solid #ccc';

                const text = document.createElement('span');
                text.textContent = `${factor.text || factor.label} (${factor.score.toFixed(3)})`;

                legendItem.appendChild(colorBox);
                legendItem.appendChild(text);
                legendContainer.appendChild(legendItem);
            });
        };

        // 国家列表
        const countries = ['Afghanistan', 'Albania', 'Algeria', 'Angola', 'Antigua and Barbuda', 'Argentina', 'Australia', 'Austria', 'The Bahamas', 'Bangladesh', 'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 'Bolivia', 'Bosnia and Herzegovina', 'Botswana', 'Brazil', 'Bulgaria', 'Burkina Faso', 'Cambodia', 'Cameroon', 'Canada', 'Chile', 'China', 'Colombia', 'Congo, Dem. Rep.', 'Congo, Rep.', 'Costa Rica', "Côte d'Ivoire", 'Croatia', 'Cyprus', 'Czechia', 'Denmark', 'Dominica', 'Dominican Republic', 'Ecuador', 'Egypt, Arab Rep.', 'El Salvador', 'Estonia', 'Ethiopia', 'Finland', 'France', 'Gabon', 'The Gambia', 'Georgia', 'Germany', 'Ghana', 'Greece', 'Grenada', 'Guatemala', 'Guinea', 'Guyana', 'Haiti', 'Honduras', 'Hong Kong SAR, China', 'Hungary', 'India', 'Indonesia', 'Iran, Islamic Rep.', 'Ireland', 'Italy', 'Jamaica', 'Japan', 'Jordan', 'Kazakhstan', 'Kenya', 'Korea, Rep.', 'Kosovo', 'Kuwait', 'Kyrgyz Republic', 'Latvia', 'Lebanon', 'Liberia', 'Lithuania', 'Luxembourg', 'Madagascar', 'Malawi', 'Malaysia', 'Mali', 'Malta', 'Mauritania', 'Mauritius', 'Mexico', 'Moldova', 'Mongolia', 'Montenegro', 'Morocco', 'Mozambique', 'Myanmar', 'Namibia', 'Nepal', 'Netherlands', 'New Zealand', 'Nicaragua', 'Niger', 'Nigeria', 'North Macedonia', 'Norway', 'Pakistan', 'Panama', 'Paraguay', 'Peru', 'Philippines', 'Poland', 'Portugal', 'Romania', 'Russian Federation', 'Rwanda', 'Senegal', 'Serbia', 'Sierra Leone', 'Singapore', 'Slovak Republic', 'Slovenia', 'South Africa', 'Spain', 'Sri Lanka', 'St. Kitts and Nevis', 'St. Lucia', 'St. Vincent and the Grenadines', 'Sudan', 'Suriname', 'Sweden', 'Tanzania', 'Thailand', 'Togo', 'Trinidad and Tobago', 'Tunisia', 'Türkiye', 'Uganda', 'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States', 'Uruguay', 'Uzbekistan', 'Venezuela, RB', 'Vietnam', 'Zambia', 'Zimbabwe'];

        // 填充国家选择下拉框
        const countrySelect = document.getElementById('country-select');
        countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country;
            option.textContent = country;
            countrySelect.appendChild(option);
        });

        // 加载数据并渲染日晕图的函数
        const loadCountryData = function(countryName) {
            d3.json(`data/${countryName}.json`).then(data => {
                // 使用分区布局
                root = d3.partition()
                    .size([2 * Math.PI, radius])
                    (d3.hierarchy(data)
                        .sum(d => d.score || 1)  // 使用score作为权重
                        .sort((a, b) => (b.data.score || 0) - (a.data.score || 0)));

                render(data);
            }).catch(error => {
                console.error('Error loading data:', error);
                alert('无法加载该国家的数据');
            });
        };

        // 监听国家选择变化
        countrySelect.addEventListener('change', function() {
            const selectedCountry = this.value;
            if (selectedCountry) {
                loadCountryData(selectedCountry);
            }
        });

        // 默认加载中国的数据
        countrySelect.value = 'China';
        loadCountryData('China');
    </script>
</body>

</html>